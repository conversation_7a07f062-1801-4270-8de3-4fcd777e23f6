using System;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Application.Section.Extensions;
using Application.Services.BlobStorage;
using Database.Repositories.Section;
using Database.Repositories.StaticMaterial;
using Database.Repositories.User;
using Domain.ValueObjects;
using Microsoft.Extensions.Options;
using Model.Section.UpdateDxf.Request;
using static Application.Core.UseCaseResponseFactory<System.Guid>;
using static Application.Section.Constants;

namespace Application.Section.UpdateDxf;

public sealed class UpdateSectionDxfUseCase : IUpdateSectionDxfUseCase
{
    private readonly ISectionRepository _sectionRepository;
    private readonly IStaticMaterialRepository _staticMaterialRepository;
    private readonly IBlobStorageService _blobService;
    private readonly BlobStorageOptions _blobOptions;
    private readonly IUserRepository _userRepository;
    private readonly UpdateSectionDxfRequestValidator _requestValidator = new();

    public UpdateSectionDxfUseCase(
        ISectionRepository sectionRepository,
        IBlobStorageService blobService,
        IOptions<BlobStorageOptions> blobOptions,
        IStaticMaterialRepository staticMaterialRepository,
        IUserRepository userRepository)
    {
        _sectionRepository = sectionRepository;
        _blobService = blobService;
        _blobOptions = blobOptions.Value;
        _staticMaterialRepository = staticMaterialRepository;
        _userRepository = userRepository;
    }

    public async Task<UseCaseResponse<Guid>> Execute(
        UpdateSectionDxfRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(Guid.Empty);
            }

            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(Guid.Empty,
                    validationResult.Errors.ToErrorMessages());
            }

            var sectionFromDb = await _sectionRepository.GetAsync(request.Id);

            if (sectionFromDb == null)
            {
                return BadRequest(Guid.Empty, "000",
                    "Section not found in database.");
            }

            if (!request.RequestedBySuperSupport
                && request.RequestedUserStructures.All(id =>
                    id != sectionFromDb.StructureId))
            {
                return Forbidden(Guid.Empty, "000",
                    "You do not have permission to update this section.");
            }

            request.StructureId = sectionFromDb.Structure.Id;

            var materials = await _staticMaterialRepository
                .GetByStructure(request.StructureId);

            foreach (var reviewFromRequest in request.Reviews)
            {
                var reviewFromDb =
                    sectionFromDb.Reviews.FirstOrDefault(x => x.Id == reviewFromRequest.Id);

                if (reviewFromDb == null)
                {
                    return BadRequest(Guid.Empty, "000",
                        $"Review {reviewFromRequest.Id} not found in section {sectionFromDb.Id}");
                }

                if (reviewFromDb.IsUnderConstruction)
                {
                    if (reviewFromRequest.Drawing is not null)
                    {
                        return BadRequest(Guid.Empty, "000",
                            $"Review {reviewFromRequest.Id} is under construction. Drawings can only be used in construction stages.");
                    }
                    
                    if (!reviewFromRequest.ConstructionStages.Any())
                    {
                        return BadRequest(Guid.Empty, "000",
                            $"Review {reviewFromRequest.Id} has construction stages and the request does not.");
                    }
                    
                    foreach (var stageFromRequest in reviewFromRequest.ConstructionStages)
                    {
                        var dxfValidationResult = stageFromRequest.Drawing.IsValidSection(materials);

                        if (!string.IsNullOrEmpty(dxfValidationResult))
                        {
                            return BadRequest(Guid.Empty, "000",
                                dxfValidationResult);
                        }
                    }
                }
                else
                {
                    if (reviewFromRequest.ConstructionStages.Any())
                    {
                        return BadRequest(Guid.Empty, "000",
                            $"Review {reviewFromRequest.Id} does not have construction stages and the request does.");
                    }
                    
                    var dxfValidationResult = reviewFromRequest.Drawing.IsValidSection(materials);

                    if (!string.IsNullOrEmpty(dxfValidationResult))
                    {
                        return BadRequest(Guid.Empty, "000",
                            dxfValidationResult);
                    }
                }
            }

            await UpdateProperties(sectionFromDb, request);

            await _sectionRepository.UpdateAsync(sectionFromDb);

            return Ok(sectionFromDb.Id);
        }
        catch (Exception e)
        {
            return InternalServerError(Guid.Empty,
                errors: e.ToErrorMessages("000"));
        }
    }

    private async Task UpdateProperties(
        Domain.Entities.Section sectionFromDb,
        UpdateSectionDxfRequest sectionFromRequest)
    {
        var clone = (Domain.Entities.Section)sectionFromDb.Clone();

        foreach (var reviewFromRequest in sectionFromRequest.Reviews)
        {
            var drawing = new File();
            var reviewFromDb =
                sectionFromDb.Reviews.FirstOrDefault(x => x.Id == reviewFromRequest.Id)
                ?? throw new Exception(
                    $"Review {reviewFromRequest.Id} not found in section {sectionFromDb.Id}");

            if (reviewFromRequest.StartDate.HasValue)
            {
                reviewFromDb.StartDate = reviewFromRequest.StartDate.Value;
            }

            if (reviewFromDb.Drawing != null && reviewFromRequest.Drawing == null)
            {
                var deletedFile = await _blobService
                    .DeleteAsync(reviewFromDb.Drawing.UniqueName,
                        _blobOptions.ClientsContainer);

                if (!deletedFile)
                {
                    throw new Exception(
                        "The drawing could not be deleted from the blob storage.");
                }

                reviewFromDb.SetDrawing(null);
            }
            else if (reviewFromRequest.Drawing != null)
            {
                drawing.UniqueName = reviewFromDb.Drawing?.UniqueName ??
                                     Guid.NewGuid().ToString("N");
                drawing.Name = reviewFromRequest.Drawing.Name;
                drawing.Base64 = reviewFromRequest.Drawing?.Base64;

                var blobUrl = await _blobService
                    .UploadAsync(
                        Convert.FromBase64String(reviewFromRequest.Drawing.Base64),
                        drawing.UniqueName, _blobOptions.ClientsContainer);

                if (string.IsNullOrEmpty(blobUrl))
                {
                    throw new Exception(
                        "Failed to upload file to blob storage.");
                }

                reviewFromDb.SetDrawing(drawing);
            }

            foreach (var stageFromRequest in reviewFromRequest.ConstructionStages)
            {
                var stageFromDb =
                    reviewFromDb.ConstructionStages.FirstOrDefault(x =>
                        x.Id == stageFromRequest.Id)
                    ?? throw new Exception(
                        $"Stage {stageFromRequest.Id} not found in review {reviewFromRequest.Id}");

                stageFromDb.IsCurrentStage = stageFromRequest.IsCurrentStage;
                
                if (stageFromDb.Drawing != null && stageFromRequest.Drawing == null)
                {
                    var deletedFile = await _blobService
                        .DeleteAsync(stageFromDb.Drawing.UniqueName,
                            _blobOptions.ClientsContainer);

                    if (!deletedFile)
                    {
                        throw new Exception(
                            "The drawing could not be deleted from the blob storage.");
                    }

                    stageFromDb.SetDrawing(null);
                }
                else if (stageFromRequest.Drawing != null)
                {
                    var drawingStage = new File
                    {
                        UniqueName = stageFromDb.Drawing?.UniqueName ??
                                     Guid.NewGuid().ToString("N"),
                        Name = stageFromRequest.Drawing.Name,
                        Base64 = stageFromRequest.Drawing?.Base64
                    };

                    var blobUrl = await _blobService
                        .UploadAsync(
                            Convert.FromBase64String(stageFromRequest.Drawing.Base64),
                            drawingStage.UniqueName,
                            _blobOptions.ClientsContainer);

                    if (string.IsNullOrEmpty(blobUrl))
                    {
                        throw new Exception(
                            "Failed to upload file to blob storage.");
                    }

                    stageFromDb.SetDrawing(drawingStage);
                }
            }
        }

        var changeText = CompareLogic.GetChangeHistory(clone, sectionFromDb);

        if (!string.IsNullOrEmpty(changeText))
        {
            sectionFromDb.History.Add(new()
            {
                Section = sectionFromDb,
                ModifiedBy = new()
                {
                    Id = sectionFromRequest.RequestedBy
                },
                Changes = changeText.Length > 8000
                    ? changeText[..8000]
                    : changeText
            });
        }
    }

}